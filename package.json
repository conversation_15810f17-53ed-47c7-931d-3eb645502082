{"name": "influencerflow-automate-campaigns", "version": "1.0.0", "private": true, "dependencies": {"@convostack/langchain-memory": "^0.0.56", "@langchain/core": "^0.3.57", "@langchain/openai": "^0.5.11", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.0.7", "@supabase/supabase-js": "^2.39.3", "@tanstack/react-query": "^5.18.1", "@types/uuid": "^10.0.0", "axios": "^1.6.7", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^11.0.3", "input-otp": "^1.4.2", "langchain": "^0.3.27", "lucide-react": "^0.323.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-resizable-panels": "^3.0.2", "react-router-dom": "^6.22.0", "recharts": "^2.11.0", "sonner": "^1.4.0", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.22.4", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^20.11.16", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.17", "lovable-tagger": "^1.1.8", "postcss": "^8.4.35", "vite": "^5.0.12"}, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "build:dev": "vite build --mode development", "update-browserslist": "npx update-browserslist-db@latest"}}