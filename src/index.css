@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 255 255 255; /* White #FFFFFF */
    --foreground: 17 24 39; /* Dark gray #111827 */

    --card: 255 255 255;
    --card-foreground: 17 24 39;

    --popover: 255 255 255;
    --popover-foreground: 17 24 39;

    --primary: 255 98 67; /* Orange #FF6243 */
    --primary-foreground: 255 255 255;

    --secondary: 249 250 251;
    --secondary-foreground: 17 24 39;

    --muted: 249 250 251;
    --muted-foreground: 107 114 128;

    --accent: 255 98 67;
    --accent-foreground: 255 255 255;

    --destructive: 220 38 38;
    --destructive-foreground: 255 255 255;

    --border: 229 231 235;
    --input: 229 231 235;
    --ring: 255 98 67;

    --radius: 0.75rem;

    --sidebar-background: 255 255 255;
    --sidebar-foreground: 17 24 39;
    --sidebar-primary: 255 98 67;
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 249 250 251;
    --sidebar-accent-foreground: 17 24 39;
    --sidebar-border: 229 231 235;
    --sidebar-ring: 255 98 67;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-white text-gray-900 font-inter;
    scroll-behavior: smooth;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-space;
  }

  .font-kpi {
    @apply font-unbounded;
  }
}

@layer components {
  .btn-orange {
    @apply bg-coral hover:bg-coral/90 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg;
  }

  .btn-outline {
    @apply border-2 border-coral text-coral hover:bg-coral hover:text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300;
  }

  .gradient-border {
    @apply relative before:absolute before:inset-0 before:p-[2px] before:bg-gradient-to-r before:from-coral before:to-orange-400 before:rounded-xl;
  }

  .gradient-border > * {
    @apply relative bg-white rounded-xl;
  }

  .section-padding {
    @apply py-20 lg:py-32;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Updated gradient text for homepage sections */
  .gradient-text {
    @apply bg-gradient-to-r from-coral to-orange-400 bg-clip-text text-transparent;
  }

  /* Updated button styles for homepage */
  .primary-button {
    @apply bg-coral hover:bg-coral/90 text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300 transform hover:scale-105;
  }

  .secondary-button {
    @apply border-2 border-coral text-coral hover:bg-coral hover:text-white font-semibold px-8 py-4 rounded-xl transition-all duration-300;
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}

.animate-pulse {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-pulse-delay-200 {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 200ms;
}

.animate-pulse-delay-400 {
  animation: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation-delay: 400ms;
}
