import { supabase } from '@/integrations/supabase/client';
import { cleanEnvVar } from '@/lib/utils';

export interface APItemplatePayload {
  template_id: string;
  data: {
    // Campaign data
    campaignName: string;
    brandName: string;
    campaignDescription?: string;
    campaignGoals?: string;
    deliverables?: string;
    timeline?: string;
    budget?: number;
    
    // Influencer data
    influencerName: string;
    influencerHandle: string;
    influencerPlatform: string;
    influencerFollowers?: number;
    influencerEngagementRate?: number;
    
    // Contract specific data
    contractId: string;
    fee: number;
    deadline: string;
    startDate: string;
    endDate: string;
    paymentTerms?: string;
    
    // Additional fields
    createdDate: string;
    brandRepresentative?: string;
    specialInstructions?: string;
  };
  export_type?: 'pdf' | 'png' | 'jpg';
  expiration?: number;
  output_html?: boolean;
  output_format?: 'url' | 'base64';
}

export interface APItemplateResponse {
  status: 'success' | 'error';
  download_url?: string;
  download_url_png?: string;
  download_url_jpg?: string;
  transaction_ref?: string;
  error?: string;
  message?: string;
}

class APItemplateService {
  private readonly baseUrl = 'https://rest.apitemplate.io';
  private readonly apiKey: string;

  constructor() {
    // API key should be stored in environment variables
    // Clean the API key by removing quotes and trimming whitespace
    this.apiKey = cleanEnvVar(import.meta.env.VITE_APITEMPLATE_API_KEY);
    if (!this.apiKey) {
      console.warn('APItemplate API key not found. Please set VITE_APITEMPLATE_API_KEY environment variable.');
    }
  }

  async createContract(payload: APItemplatePayload): Promise<APItemplateResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/v2/create-pdf`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': this.apiKey,
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`APItemplate API error: ${response.status} ${response.statusText}`);
      }

      const result: APItemplateResponse = await response.json();
      return result;
    } catch (error) {
      console.error('APItemplate service error:', error);
      throw error;
    }
  }

  async downloadAndStorePDF(downloadUrl: string, contractId: string, campaignId: string, influencerId: string): Promise<string> {
    try {
      // Download PDF from APItemplate
      const pdfResponse = await fetch(downloadUrl);
      if (!pdfResponse.ok) {
        throw new Error('Failed to download PDF from APItemplate');
      }

      const pdfBlob = await pdfResponse.blob();
      const pdfBuffer = await pdfBlob.arrayBuffer();

      // Generate storage path
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const storagePath = `contracts/${campaignId}/${influencerId}_${contractId}_${timestamp}.pdf`;

      // Upload to Supabase storage
      const { error: uploadError } = await supabase.storage
        .from('contracts')
        .upload(storagePath, pdfBuffer, {
          contentType: 'application/pdf',
          upsert: false,
        });

      if (uploadError) {
        throw new Error(`Failed to upload PDF to storage: ${uploadError.message}`);
      }

      return storagePath;
    } catch (error) {
      console.error('Error downloading and storing PDF:', error);
      throw error;
    }
  }

  // Helper method to create contract payload from campaign and influencer data
  createContractPayload(
    templateId: string,
    campaign: any,
    influencer: any,
    contractData: {
      fee: number;
      deadline: string;
      startDate?: string;
      endDate?: string;
      paymentTerms?: string;
      specialInstructions?: string;
    }
  ): APItemplatePayload {
    const contractId = `CTR_${Date.now()}_${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
    const startDate = contractData.startDate || new Date().toISOString().split('T')[0];
    const endDate = contractData.endDate || contractData.deadline;

    return {
      template_id: templateId,
      data: {
        // Campaign data
        campaignName: campaign.name || 'N/A',
        brandName: campaign.brand || 'N/A',
        campaignDescription: campaign.description || '',
        campaignGoals: campaign.goals || '',
        deliverables: campaign.deliverables || '',
        timeline: campaign.timeline || '',
        budget: campaign.budget || 0,
        
        // Influencer data
        influencerName: influencer.name || 'N/A',
        influencerHandle: influencer.handle || 'N/A',
        influencerPlatform: influencer.platform || 'N/A',
        influencerFollowers: influencer.followers_count || 0,
        influencerEngagementRate: influencer.engagement_rate || 0,
        
        // Contract specific data
        contractId,
        fee: contractData.fee,
        deadline: contractData.deadline,
        startDate,
        endDate,
        paymentTerms: contractData.paymentTerms || 'Payment due within 30 days of deliverable completion',
        
        // Additional fields
        createdDate: new Date().toLocaleDateString(),
        brandRepresentative: 'Brand Representative', // This could be dynamic
        specialInstructions: contractData.specialInstructions || '',
      },
      export_type: 'pdf',
      output_format: 'url',
      expiration: 3600, // 1 hour expiration for download URL
    };
  }
}

export default new APItemplateService();
