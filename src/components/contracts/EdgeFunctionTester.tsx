import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Loader2, CheckCircle, XCircle } from 'lucide-react';

export const EdgeFunctionTester = () => {
  const { toast } = useToast();
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);

  const testEdgeFunction = async (functionName: string) => {
    setTesting(true);
    setTestResults(null);

    try {
      console.log(`Testing ${functionName}...`);
      
      const testData = {
        campaignId: '2b765c21-5633-4317-9d22-ced5a4eb66ed', // CogentX campaign
        influencerId: '6abba538-98e3-4c88-88bc-e02adbf5753f', // Alex Gaming
        fee: 1000,
        deadline: '2024-12-31',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        paymentTerms: 'Test payment terms',
        specialInstructions: 'Test instructions',
      };

      const { data, error } = await supabase.functions.invoke(functionName, {
        body: testData,
      });

      console.log(`${functionName} response:`, { data, error });

      if (error) {
        throw error;
      }

      setTestResults({
        success: true,
        functionName,
        data,
        error: null,
      });

      toast({
        title: 'Test Successful',
        description: `${functionName} is working correctly!`,
      });

    } catch (error) {
      console.error(`${functionName} test failed:`, error);
      
      setTestResults({
        success: false,
        functionName,
        data: null,
        error: error.message || 'Unknown error',
      });

      toast({
        title: 'Test Failed',
        description: `${functionName} test failed: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setTesting(false);
    }
  };

  const testSupabaseConnection = async () => {
    setTesting(true);
    try {
      // Test basic database connection with a simple query
      const { data, error } = await supabase
        .from('campaigns')
        .select('id, name')
        .limit(1);

      if (error) throw error;

      toast({
        title: 'Database Connection OK',
        description: 'Supabase database is accessible',
      });

      setTestResults({
        success: true,
        functionName: 'Database Connection',
        data: { message: 'Database accessible', campaigns: data?.length || 0 },
        error: null,
      });

    } catch (error) {
      console.error('Database test failed:', error);

      setTestResults({
        success: false,
        functionName: 'Database Connection',
        data: null,
        error: error.message,
      });

      toast({
        title: 'Database Test Failed',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setTesting(false);
    }
  };

  return (
    <Card className="mb-4 border-blue-200">
      <CardHeader>
        <CardTitle className="text-sm text-blue-900">Edge Function Tester</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <Button
            size="sm"
            onClick={() => testEdgeFunction('test-contract')}
            disabled={testing}
            variant="outline"
          >
            {testing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
            Test Simple Function
          </Button>

          <Button
            size="sm"
            onClick={() => testEdgeFunction('create-apitemplate-contract')}
            disabled={testing}
            variant="outline"
          >
            {testing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
            Test Main Function
          </Button>

          <Button
            size="sm"
            onClick={testSupabaseConnection}
            disabled={testing}
            variant="outline"
          >
            {testing ? <Loader2 className="w-4 h-4 animate-spin mr-2" /> : null}
            Test Database
          </Button>
        </div>

        {testResults && (
          <div className="mt-4 p-3 rounded-lg border">
            <div className="flex items-center space-x-2 mb-2">
              {testResults.success ? (
                <CheckCircle className="w-4 h-4 text-green-600" />
              ) : (
                <XCircle className="w-4 h-4 text-red-600" />
              )}
              <span className="font-medium text-sm">
                {testResults.functionName} - {testResults.success ? 'Success' : 'Failed'}
              </span>
            </div>

            {testResults.success && testResults.data && (
              <div className="text-xs bg-green-50 p-2 rounded">
                <strong>Response:</strong>
                <pre className="mt-1 whitespace-pre-wrap">
                  {JSON.stringify(testResults.data, null, 2)}
                </pre>
              </div>
            )}

            {!testResults.success && testResults.error && (
              <div className="text-xs bg-red-50 p-2 rounded">
                <strong>Error:</strong>
                <pre className="mt-1 whitespace-pre-wrap text-red-700">
                  {testResults.error}
                </pre>
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-600 space-y-1">
          <div><strong>Instructions:</strong></div>
          <div>1. First test the database connection</div>
          <div>2. Test the simple function to verify Edge Function connectivity</div>
          <div>3. Test the main function to verify APItemplate integration</div>
          <div>4. Check browser console for detailed logs</div>
        </div>
      </CardContent>
    </Card>
  );
};
