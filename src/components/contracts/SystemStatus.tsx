import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { CheckCircle, XCircle, AlertCircle, Info } from 'lucide-react';

export const SystemStatus = () => {
  const [status, setStatus] = useState<any>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkSystemStatus();
  }, []);

  const checkSystemStatus = async () => {
    setLoading(true);
    const checks: any = {};

    // Check 1: Environment Variables (Critical)
    checks.envVars = {
      url: !!import.meta.env.VITE_SUPABASE_URL,
      anonKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
      critical: true,
    };

    // Check 2: Database Connection (Critical)
    try {
      const { error } = await supabase
        .from('campaigns')
        .select('id')
        .limit(1);
      
      checks.database = {
        working: !error,
        error: error?.message,
        critical: true,
      };
    } catch (error) {
      checks.database = {
        working: false,
        error: error.message,
        critical: true,
      };
    }

    // Check 3: Campaign Data (Important)
    try {
      const { data, error } = await supabase
        .from('campaigns')
        .select('id, name')
        .limit(5);
      
      checks.campaigns = {
        working: !error,
        count: data?.length || 0,
        error: error?.message,
        critical: false,
      };
    } catch (error) {
      checks.campaigns = {
        working: false,
        error: error.message,
        critical: false,
      };
    }

    // Check 4: Auth (Optional - not critical)
    try {
      const { data: { user } } = await supabase.auth.getUser();
      checks.auth = {
        loggedIn: !!user,
        user: user ? { email: user.email } : null,
        critical: false,
      };
    } catch (error) {
      checks.auth = {
        loggedIn: false,
        error: error.message,
        critical: false,
      };
    }

    setStatus(checks);
    setLoading(false);
  };

  const getStatusIcon = (working: boolean, critical: boolean) => {
    if (working) {
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    } else if (critical) {
      return <XCircle className="w-4 h-4 text-red-600" />;
    } else {
      return <AlertCircle className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getOverallStatus = () => {
    const criticalIssues = Object.values(status).filter((check: any) => 
      check.critical && !check.working
    ).length;

    if (criticalIssues > 0) {
      return { status: 'error', message: 'Critical issues detected', color: 'red' };
    }

    const dbWorking = status.database?.working;
    const envWorking = status.envVars?.url && status.envVars?.anonKey;

    if (dbWorking && envWorking) {
      return { status: 'success', message: 'System ready for contract creation', color: 'green' };
    }

    return { status: 'warning', message: 'Some issues detected', color: 'yellow' };
  };

  if (loading) {
    return (
      <Card className="mb-4 border-blue-200">
        <CardContent className="p-4">
          <div className="text-sm text-gray-600">Checking system status...</div>
        </CardContent>
      </Card>
    );
  }

  const overall = getOverallStatus();

  return (
    <Card className={`mb-4 border-${overall.color}-200`}>
      <CardHeader>
        <CardTitle className={`text-sm text-${overall.color}-900 flex items-center`}>
          {overall.status === 'success' && <CheckCircle className="w-4 h-4 mr-2 text-green-600" />}
          {overall.status === 'error' && <XCircle className="w-4 h-4 mr-2 text-red-600" />}
          {overall.status === 'warning' && <AlertCircle className="w-4 h-4 mr-2 text-yellow-600" />}
          System Status: {overall.message}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Critical Systems */}
        <div className="space-y-2">
          <div className="text-xs font-medium text-gray-700">Critical Systems</div>
          
          <div className="flex items-center space-x-2">
            {getStatusIcon(status.envVars?.url && status.envVars?.anonKey, true)}
            <span className="text-xs">
              Environment Configuration: {status.envVars?.url && status.envVars?.anonKey ? 'OK' : 'Missing variables'}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            {getStatusIcon(status.database?.working, true)}
            <span className="text-xs">
              Database Connection: {status.database?.working ? 'Connected' : 'Failed'}
            </span>
            {status.database?.error && (
              <span className="text-xs text-red-600">({status.database.error})</span>
            )}
          </div>
        </div>

        {/* Optional Systems */}
        <div className="space-y-2">
          <div className="text-xs font-medium text-gray-700">Optional Systems</div>
          
          <div className="flex items-center space-x-2">
            {getStatusIcon(status.campaigns?.working, false)}
            <span className="text-xs">
              Campaign Data: {status.campaigns?.working ? `${status.campaigns.count} campaigns found` : 'No access'}
            </span>
          </div>

          <div className="flex items-center space-x-2">
            <Info className="w-4 h-4 text-blue-600" />
            <span className="text-xs">
              Authentication: {status.auth?.loggedIn ? `Logged in as ${status.auth.user?.email}` : 'Anonymous mode (OK)'}
            </span>
          </div>
        </div>

        {/* Action Items */}
        {overall.status === 'success' && (
          <div className="bg-green-50 p-2 rounded text-xs">
            ✅ <strong>Ready to create contracts!</strong> You can use either the Edge Function method or Direct Contract Creator.
          </div>
        )}

        {overall.status === 'error' && (
          <div className="bg-red-50 p-2 rounded text-xs">
            ❌ <strong>Fix required:</strong> Check environment variables and restart development server.
          </div>
        )}

        {overall.status === 'warning' && (
          <div className="bg-yellow-50 p-2 rounded text-xs">
            ⚠️ <strong>Partial functionality:</strong> Basic features work, some advanced features may be limited.
          </div>
        )}
      </CardContent>
    </Card>
  );
};
