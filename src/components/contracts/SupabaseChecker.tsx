import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

export const SupabaseChecker = () => {
  const [checks, setChecks] = useState<any>({});
  const [loading, setLoading] = useState(false);

  const runChecks = async () => {
    setLoading(true);
    const results: any = {};

    // Check 1: Environment variables
    results.envVars = {
      supabaseUrl: !!import.meta.env.VITE_SUPABASE_URL,
      supabaseAnonKey: !!import.meta.env.VITE_SUPABASE_ANON_KEY,
      apitemplateKey: !!import.meta.env.VITE_APITEMPLATE_API_KEY,
      templateId: !!import.meta.env.VITE_APITEMPLATE_TEMPLATE_ID,
    };

    // Check 2: Supabase client initialization
    try {
      results.clientInit = {
        success: !!supabase,
        url: supabase?.supabaseUrl || 'Not available',
        key: supabase?.supabaseKey ? 'Set' : 'Not set',
      };
    } catch (error) {
      results.clientInit = {
        success: false,
        error: error.message,
      };
    }

    // Check 3: Basic database query
    try {
      console.log('Testing database connection...');
      const { data, error } = await supabase
        .from('campaigns')
        .select('id')
        .limit(1);

      console.log('Database query result:', { data, error });

      results.dbQuery = {
        success: !error,
        data: data?.length || 0,
        error: error?.message || null,
        details: error ? `Code: ${error.code}, Details: ${error.details}` : null,
      };
    } catch (error) {
      console.error('Database query failed:', error);
      results.dbQuery = {
        success: false,
        error: error.message,
        details: `Type: ${error.constructor.name}`,
      };
    }

    // Check 4: Auth status (optional - missing auth is OK for anonymous mode)
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      results.auth = {
        success: true, // Always success - missing auth is OK
        user: user ? { id: user.id, email: user.email } : null,
        error: error?.message || null,
        isAnonymous: !user,
      };
    } catch (error) {
      results.auth = {
        success: true, // Still OK - we support anonymous mode
        error: error.message,
        isAnonymous: true,
      };
    }

    // Check 5: Edge Function URL construction
    try {
      const functionUrl = `${supabase.supabaseUrl}/functions/v1/test-contract`;
      results.edgeFunctionUrl = {
        success: true,
        url: functionUrl,
      };
    } catch (error) {
      results.edgeFunctionUrl = {
        success: false,
        error: error.message,
      };
    }

    setChecks(results);
    setLoading(false);
  };

  useEffect(() => {
    runChecks();
  }, []);

  const getIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="w-4 h-4 text-green-600" />
    ) : (
      <XCircle className="w-4 h-4 text-red-600" />
    );
  };

  return (
    <Card className="mb-4 border-orange-200">
      <CardHeader>
        <CardTitle className="text-sm text-orange-900 flex items-center">
          <AlertCircle className="w-4 h-4 mr-2" />
          Supabase Configuration Checker
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button size="sm" onClick={runChecks} disabled={loading}>
          {loading ? 'Checking...' : 'Run Checks'}
        </Button>

        {Object.keys(checks).length > 0 && (
          <div className="space-y-3 text-xs">
            {/* Environment Variables */}
            <div className="border rounded p-2">
              <div className="font-medium mb-2">Environment Variables</div>
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  {getIcon(checks.envVars?.supabaseUrl)}
                  <span>VITE_SUPABASE_URL: {checks.envVars?.supabaseUrl ? 'Set' : 'Missing'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getIcon(checks.envVars?.supabaseAnonKey)}
                  <span>VITE_SUPABASE_ANON_KEY: {checks.envVars?.supabaseAnonKey ? 'Set' : 'Missing'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getIcon(checks.envVars?.apitemplateKey)}
                  <span>VITE_APITEMPLATE_API_KEY: {checks.envVars?.apitemplateKey ? 'Set' : 'Missing'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  {getIcon(checks.envVars?.templateId)}
                  <span>VITE_APITEMPLATE_TEMPLATE_ID: {checks.envVars?.templateId ? 'Set' : 'Missing'}</span>
                </div>
              </div>
            </div>

            {/* Client Initialization */}
            <div className="border rounded p-2">
              <div className="font-medium mb-2">Supabase Client</div>
              <div className="flex items-center space-x-2">
                {getIcon(checks.clientInit?.success)}
                <span>Client initialized: {checks.clientInit?.success ? 'Yes' : 'No'}</span>
              </div>
              {checks.clientInit?.url && (
                <div className="text-gray-600 mt-1">URL: {checks.clientInit.url}</div>
              )}
              {checks.clientInit?.error && (
                <div className="text-red-600 mt-1">Error: {checks.clientInit.error}</div>
              )}
            </div>

            {/* Database Query */}
            <div className="border rounded p-2">
              <div className="font-medium mb-2">Database Connection</div>
              <div className="flex items-center space-x-2">
                {getIcon(checks.dbQuery?.success)}
                <span>Database query: {checks.dbQuery?.success ? 'Success' : 'Failed'}</span>
              </div>
              {checks.dbQuery?.success && (
                <div className="text-gray-600 mt-1">Found {checks.dbQuery.data} campaigns</div>
              )}
              {checks.dbQuery?.error && (
                <div className="text-red-600 mt-1">
                  <div>Error: {checks.dbQuery.error}</div>
                  {checks.dbQuery.details && (
                    <div className="text-xs mt-1">{checks.dbQuery.details}</div>
                  )}
                </div>
              )}
            </div>

            {/* Authentication */}
            <div className="border rounded p-2">
              <div className="font-medium mb-2">Authentication (Optional)</div>
              <div className="flex items-center space-x-2">
                {getIcon(checks.auth?.success)}
                <span>Auth status: {checks.auth?.isAnonymous ? 'Anonymous Mode' : 'Logged In'}</span>
              </div>
              {checks.auth?.user && (
                <div className="text-gray-600 mt-1">
                  User: {checks.auth.user.email} ({checks.auth.user.id})
                </div>
              )}
              {checks.auth?.isAnonymous && (
                <div className="text-green-600 mt-1">✅ Anonymous mode enabled - contracts can be created without login</div>
              )}
              {checks.auth?.error && (
                <div className="text-gray-500 mt-1 text-xs">Note: {checks.auth.error} (This is normal for anonymous users)</div>
              )}
            </div>

            {/* Edge Function URL */}
            <div className="border rounded p-2">
              <div className="font-medium mb-2">Edge Function URL</div>
              <div className="flex items-center space-x-2">
                {getIcon(checks.edgeFunctionUrl?.success)}
                <span>URL construction: {checks.edgeFunctionUrl?.success ? 'Success' : 'Failed'}</span>
              </div>
              {checks.edgeFunctionUrl?.url && (
                <div className="text-gray-600 mt-1 break-all">URL: {checks.edgeFunctionUrl.url}</div>
              )}
            </div>
          </div>
        )}

        <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
          <strong>Next Steps:</strong>
          <div>1. Ensure all environment variables are set correctly</div>
          <div>2. Check if your Supabase project is accessible</div>
          <div>3. Verify Edge Functions are deployed</div>
          <div>4. Check network connectivity</div>
        </div>
      </CardContent>
    </Card>
  );
};
