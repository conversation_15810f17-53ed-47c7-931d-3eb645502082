import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { RefreshCw, Wifi, WifiOff } from 'lucide-react';

export const QuickConnectionTest = () => {
  const { toast } = useToast();
  const [testing, setTesting] = useState(false);
  const [result, setResult] = useState<any>(null);

  const testConnection = async () => {
    setTesting(true);
    setResult(null);

    try {
      console.log('=== Quick Connection Test ===');
      console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
      console.log('Anon Key:', import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Missing');

      // Test 1: Simple ping
      console.log('Testing basic connection...');
      const startTime = Date.now();
      
      const { data, error } = await supabase
        .from('campaigns')
        .select('count')
        .limit(0);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      console.log('Connection test result:', { data, error, responseTime });

      if (error) {
        throw new Error(`Database error: ${error.message} (Code: ${error.code})`);
      }

      setResult({
        success: true,
        responseTime,
        message: 'Connection successful!',
      });

      toast({
        title: 'Connection Test Passed',
        description: `Database is accessible (${responseTime}ms)`,
      });

    } catch (error) {
      console.error('Connection test failed:', error);
      
      setResult({
        success: false,
        error: error.message,
        details: error.stack,
      });

      toast({
        title: 'Connection Test Failed',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setTesting(false);
    }
  };

  const reloadPage = () => {
    window.location.reload();
  };

  return (
    <Card className="mb-4 border-purple-200">
      <CardHeader>
        <CardTitle className="text-sm text-purple-900 flex items-center">
          {result?.success ? (
            <Wifi className="w-4 h-4 mr-2 text-green-600" />
          ) : result?.success === false ? (
            <WifiOff className="w-4 h-4 mr-2 text-red-600" />
          ) : (
            <RefreshCw className="w-4 h-4 mr-2" />
          )}
          Quick Connection Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button
            size="sm"
            onClick={testConnection}
            disabled={testing}
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            {testing ? 'Testing...' : 'Test Connection'}
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={reloadPage}
            disabled={testing}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Reload Page
          </Button>
        </div>

        {result && (
          <div className={`p-3 rounded-lg border ${
            result.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            {result.success ? (
              <div className="text-green-800">
                <div className="font-medium">✅ Connection Successful!</div>
                <div className="text-sm mt-1">Response time: {result.responseTime}ms</div>
              </div>
            ) : (
              <div className="text-red-800">
                <div className="font-medium">❌ Connection Failed</div>
                <div className="text-sm mt-1">{result.error}</div>
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
          <div><strong>Current Configuration:</strong></div>
          <div>URL: {import.meta.env.VITE_SUPABASE_URL || 'Not set'}</div>
          <div>Anon Key: {import.meta.env.VITE_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}</div>
          <div>APItemplate Key: {import.meta.env.VITE_APITEMPLATE_API_KEY ? 'Set' : 'Not set'}</div>
          <div>Template ID: {import.meta.env.VITE_APITEMPLATE_TEMPLATE_ID || 'Not set'}</div>
        </div>

        <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
          <strong>If connection fails:</strong>
          <div>1. Check that .env file has correct VITE_SUPABASE_URL (no trailing slash)</div>
          <div>2. Verify VITE_SUPABASE_ANON_KEY is correct</div>
          <div>3. Click "Reload Page" after fixing .env file</div>
          <div>4. Check browser console for detailed errors</div>
        </div>
      </CardContent>
    </Card>
  );
};
