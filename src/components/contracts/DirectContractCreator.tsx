import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Loader2, FileText } from 'lucide-react';

interface DirectContractCreatorProps {
  campaignId: string;
  influencerId: string;
  fee: number;
  deadline: string;
  startDate?: string;
  endDate?: string;
  paymentTerms?: string;
  specialInstructions?: string;
  onSuccess?: () => void;
}

export const DirectContractCreator = ({
  campaignId,
  influencerId,
  fee,
  deadline,
  startDate,
  endDate,
  paymentTerms,
  specialInstructions,
  onSuccess,
}: DirectContractCreatorProps) => {
  const { toast } = useToast();
  const [creating, setCreating] = useState(false);

  const createContractDirectly = async () => {
    setCreating(true);
    
    try {
      console.log('Creating contract directly in database...');
      
      // Generate contract ID
      const contractId = `CTR_${Date.now()}_${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
      
      // Create contract record directly
      const contractRow = {
        brand_user_id: 'direct-user', // For testing without auth
        campaign_id: campaignId,
        influencer_id: influencerId,
        template_id: import.meta.env.VITE_APITEMPLATE_TEMPLATE_ID || 'direct-template',
        pdf_url: null, // No PDF for direct creation
        status: 'draft',
        contract_data: {
          fee: fee || 0,
          deadline: deadline || new Date().toISOString().split('T')[0],
          startDate: startDate || new Date().toISOString().split('T')[0],
          endDate: endDate || deadline || new Date().toISOString().split('T')[0],
          paymentTerms: paymentTerms || 'Payment due within 30 days',
          specialInstructions: specialInstructions || '',
          contractId,
          generated_at: new Date().toISOString(),
          creation_method: 'direct', // Mark as direct creation
        },
      };

      console.log('Inserting contract:', contractRow);

      const { data: inserted, error: contractError } = await supabase
        .from('contracts')
        .insert(contractRow)
        .select('*')
        .single();

      if (contractError) {
        console.error('Contract insert error:', contractError);
        throw new Error(`Failed to create contract: ${contractError.message}`);
      }

      console.log('Contract created successfully:', inserted);

      toast({
        title: 'Contract Created Successfully',
        description: 'Contract created directly in database (no PDF generated)',
      });

      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error creating contract directly:', error);
      
      toast({
        title: 'Direct Contract Creation Failed',
        description: error.message || 'Failed to create contract directly',
        variant: 'destructive',
      });
    } finally {
      setCreating(false);
    }
  };

  return (
    <Card className="border-green-200">
      <CardHeader>
        <CardTitle className="text-sm text-green-900 flex items-center">
          <FileText className="w-4 h-4 mr-2" />
          Direct Contract Creation (Bypass Edge Functions)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-xs text-gray-600 space-y-1">
          <div><strong>This will:</strong></div>
          <div>• Create contract record directly in database</div>
          <div>• Skip APItemplate PDF generation</div>
          <div>• Bypass Edge Function requirements</div>
          <div>• Use for testing when Edge Functions fail</div>
        </div>

        <div className="text-xs bg-green-50 p-2 rounded">
          <div><strong>Contract Details:</strong></div>
          <div>Campaign ID: {campaignId}</div>
          <div>Influencer ID: {influencerId}</div>
          <div>Fee: ₹{fee?.toLocaleString()}</div>
          <div>Deadline: {deadline}</div>
        </div>

        <Button
          onClick={createContractDirectly}
          disabled={creating || !campaignId || !influencerId || !fee || !deadline}
          className="w-full bg-green-600 hover:bg-green-700 text-white"
          size="sm"
        >
          {creating ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Creating Contract...
            </>
          ) : (
            'Create Contract Directly'
          )}
        </Button>

        <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded">
          <strong>Note:</strong> This method creates a basic contract record without PDF generation. 
          Use this for testing when Edge Functions are not working.
        </div>
      </CardContent>
    </Card>
  );
};
