// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from '@/types/supabase';

// Use environment variables instead of hardcoded values
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Validate URL and throw meaningful errors
if (!supabaseUrl) {
  throw new Error('Missing Supabase URL environment variable (VITE_SUPABASE_URL)');
}

if (!supabaseAnonKey) {
  throw new Error('Missing Supabase Anon Key environment variable (VITE_SUPABASE_ANON_KEY)');
}

// Ensure URL is properly formatted
let validatedUrl: string;
try {
  const url = new URL(supabaseUrl);
  // Remove trailing slash to prevent double slashes in function URLs
  validatedUrl = url.toString().replace(/\/$/, '');
} catch (error) {
  throw new Error(`Invalid Supabase URL format: ${error.message}`);
}

// Create and export the Supabase client
export const supabase = createClient<Database>(validatedUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
  },
});
