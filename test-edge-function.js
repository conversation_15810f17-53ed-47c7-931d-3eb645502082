// Quick test script for edge function
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://veeuscozuavvqyjqeljq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZlZXVzY296dWF2dnF5anFlbGpxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg1OTcxODIsImV4cCI6MjA2NDE3MzE4Mn0.r2WunFzzv0PwGblLy0CcGvRzgbO1YjVn5hO8lLzQp2s';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testEdgeFunction() {
  console.log('Testing edge function...');
  
  const testData = {
    campaignId: '91636d5d-fd93-4ed5-8bb3-485ffff65152',
    influencerId: 'bf6f9db4-8b5e-472b-ab56-ba22c9673e82',
    fee: 1000,
    deadline: '2024-12-31',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    paymentTerms: 'Payment due within 30 days',
    specialInstructions: 'Test contract creation'
  };

  try {
    const { data, error } = await supabase.functions.invoke('test-contract-simple', {
      body: testData
    });

    if (error) {
      console.error('Edge function error:', error);
      return;
    }

    console.log('Edge function success:', data);
  } catch (err) {
    console.error('Test failed:', err);
  }
}

testEdgeFunction();
