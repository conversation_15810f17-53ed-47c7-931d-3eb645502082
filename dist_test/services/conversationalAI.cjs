"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.conversationalAIService = void 0;
var elevenlabs_js_1 = require("@elevenlabs/elevenlabs-js");
var ELEVENLABS_API_KEY = '***************************************************';
var AGENT_ID = 'agent_01jwkpad6te50bmvfd8ax6xvqk';
var ELEVENLABS_API_URL = 'https://api.elevenlabs.io/v1';
var ConversationalAIService = /** @class */ (function () {
    function ConversationalAIService() {
        this.client = new elevenlabs_js_1.ElevenLabsClient({
            apiKey: ELEVENLABS_API_KEY,
        });
    }
    ConversationalAIService.prototype.initiateOutboundCall = function (phoneNumber, campaignData) {
        return __awaiter(this, void 0, void 0, function () {
            var firstMessage, systemPrompt, response, data, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        firstMessage = "Hello, I'm calling on behalf of ".concat(campaignData.brand_name, " regarding our ").concat(campaignData.campaign_name, " campaign. ").concat(campaignData.brief);
                        systemPrompt = "You are a professional outreach specialist for ".concat(campaignData.brand_name, ". \n        Your goal is to discuss a collaboration opportunity for the ").concat(campaignData.campaign_name, " campaign.\n        The campaign brief is: ").concat(campaignData.brief, "\n        The deliverables include: ").concat(campaignData.deliverables.join(', '), ".\n        Be professional, friendly, and respect the creator's time.");
                        return [4 /*yield*/, fetch("".concat(ELEVENLABS_API_URL, "/convai/outbound-call"), {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'xi-api-key': ELEVENLABS_API_KEY
                                },
                                body: JSON.stringify({
                                    agent_id: AGENT_ID,
                                    phone_number: phoneNumber,
                                    first_message: firstMessage,
                                    system_prompt: systemPrompt,
                                    metadata: {
                                        campaign_id: campaignData.campaign_id,
                                        campaign_name: campaignData.campaign_name,
                                        brand_name: campaignData.brand_name,
                                        deliverables: campaignData.deliverables
                                    }
                                })
                            })];
                    case 1:
                        response = _a.sent();
                        if (!response.ok) {
                            throw new Error("Failed to initiate call: ".concat(response.statusText));
                        }
                        return [4 /*yield*/, response.json()];
                    case 2:
                        data = _a.sent();
                        return [2 /*return*/, {
                                call_id: data.conversation_id,
                                status: data.status || 'initiated'
                            }];
                    case 3:
                        error_1 = _a.sent();
                        console.error('Error initiating outbound call:', error_1);
                        throw error_1;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    ConversationalAIService.prototype.getCallStatus = function (callId) {
        return __awaiter(this, void 0, void 0, function () {
            var response, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, fetch("".concat(ELEVENLABS_API_URL, "/convai/conversation/").concat(callId, "/status"), {
                                headers: {
                                    'xi-api-key': ELEVENLABS_API_KEY
                                }
                            })];
                    case 1:
                        response = _a.sent();
                        if (!response.ok) {
                            throw new Error("Failed to get call status: ".concat(response.statusText));
                        }
                        return [4 /*yield*/, response.json()];
                    case 2: return [2 /*return*/, _a.sent()];
                    case 3:
                        error_2 = _a.sent();
                        console.error('Error getting call status:', error_2);
                        throw error_2;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    ConversationalAIService.prototype.getCallTranscript = function (callId) {
        return __awaiter(this, void 0, void 0, function () {
            var response, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        return [4 /*yield*/, fetch("".concat(ELEVENLABS_API_URL, "/convai/conversation/").concat(callId, "/transcript"), {
                                headers: {
                                    'xi-api-key': ELEVENLABS_API_KEY
                                }
                            })];
                    case 1:
                        response = _a.sent();
                        if (!response.ok) {
                            throw new Error("Failed to get call transcript: ".concat(response.statusText));
                        }
                        return [4 /*yield*/, response.json()];
                    case 2: return [2 /*return*/, _a.sent()];
                    case 3:
                        error_3 = _a.sent();
                        console.error('Error getting call transcript:', error_3);
                        throw error_3;
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    return ConversationalAIService;
}());
exports.conversationalAIService = new ConversationalAIService();
