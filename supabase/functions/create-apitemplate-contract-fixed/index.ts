import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

/**
 * Cleans environment variable values by removing surrounding quotes and trimming whitespace
 */
function cleanEnvVar(value: string | null): string | null {
  if (!value) return null;
  return value.replace(/^["']|["']$/g, '').trim();
}

interface APItemplatePayload {
  template_id: string;
  data: Record<string, any>;
  export_type?: 'pdf' | 'png' | 'jpg';
  expiration?: number;
  output_format?: 'url' | 'base64';
}

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("APItemplate contract function called");

    // Generate UUID for anonymous users
    const userId = crypto.randomUUID();
    console.log("User ID:", userId);

    // Service client for database operations
    const serviceClient = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
    );

    // Parse request body
    const {
      campaignId,
      influencerId,
      fee,
      deadline,
      startDate,
      endDate,
      paymentTerms,
      specialInstructions
    } = await req.json();

    console.log("Request data:", { campaignId, influencerId, fee, deadline });

    // Validate required UUIDs
    if (!campaignId || !influencerId) {
      throw new Error("Campaign ID and Influencer ID are required");
    }

    // Get template ID from environment
    const templateId = cleanEnvVar(Deno.env.get("APITEMPLATE_TEMPLATE_ID")) || "test-template";
    console.log("Template ID:", templateId);

    // Fetch campaign data
    const { data: campaign, error: campaignError } = await serviceClient
      .from("campaigns")
      .select("*")
      .eq("id", campaignId)
      .single();

    if (campaignError || !campaign) {
      console.error("Campaign error:", campaignError);
      throw new Error("Campaign not found");
    }

    console.log("Campaign found:", campaign.name);

    // Fetch influencer data
    const { data: influencer, error: influencerError } = await serviceClient
      .from("influencers")
      .select("*")
      .eq("id", influencerId)
      .single();

    if (influencerError || !influencer) {
      console.error("Influencer error:", influencerError);
      throw new Error("Influencer not found");
    }

    console.log("Influencer found:", influencer.name);

    // Generate contract ID
    const contractId = crypto.randomUUID();

    // Call APItemplate API
    const apitemplateApiKey = cleanEnvVar(Deno.env.get("APITEMPLATE_API_KEY"));
    let storagePath = null;
    let apitemplateResult = { transaction_ref: 'test-transaction' };

    console.log("APItemplate API Key present:", !!apitemplateApiKey);

    if (apitemplateApiKey && templateId !== "test-template") {
      console.log("Calling APItemplate API...");

      try {
        const apitemplatePayload: APItemplatePayload = {
          template_id: templateId,
          data: {
            campaignName: campaign.name || 'Untitled Campaign',
            brandName: campaign.brand_name || 'Brand',
            influencerName: influencer.name || 'Influencer',
            influencerHandle: influencer.handle || '@influencer',
            contractId,
            fee: fee || 0,
            deadline: deadline || 'TBD',
            startDate: startDate || new Date().toISOString().split('T')[0],
            endDate: endDate || deadline || 'TBD',
            paymentTerms: paymentTerms || 'Payment due within 30 days',
            specialInstructions: specialInstructions || '',
            createdDate: new Date().toLocaleDateString(),
          },
          export_type: 'pdf',
          output_format: 'url',
          expiration: 3600,
        };

        const apitemplateResponse = await fetch("https://rest.apitemplate.io/v2/create-pdf", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-KEY": apitemplateApiKey,
          },
          body: JSON.stringify(apitemplatePayload),
        });

        if (!apitemplateResponse.ok) {
          const errorText = await apitemplateResponse.text();
          console.error("APItemplate API error:", errorText);
          throw new Error(`APItemplate API error: ${apitemplateResponse.status}`);
        }

        apitemplateResult = await apitemplateResponse.json();

        if (apitemplateResult.status !== 'success' || !apitemplateResult.download_url) {
          console.error("APItemplate result error:", apitemplateResult);
          throw new Error("Failed to generate PDF with APItemplate");
        }

        console.log("APItemplate success:", apitemplateResult.transaction_ref);

        // Download and store the PDF
        const pdfResponse = await fetch(apitemplateResult.download_url);
        if (!pdfResponse.ok) {
          throw new Error('Failed to download PDF from APItemplate');
        }

        const pdfBlob = await pdfResponse.blob();
        const pdfBuffer = await pdfBlob.arrayBuffer();

        // Generate storage path
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        storagePath = `contracts/${campaignId}/${influencerId}_${contractId}_${timestamp}.pdf`;

        // Upload to Supabase storage
        const { error: uploadError } = await serviceClient.storage
          .from('contracts')
          .upload(storagePath, pdfBuffer, {
            contentType: 'application/pdf',
            upsert: false,
          });

        if (uploadError) {
          console.error("Storage upload error:", uploadError);
          throw new Error(`Failed to store PDF: ${uploadError.message}`);
        }

        console.log("PDF uploaded successfully to:", storagePath);
      } catch (error) {
        console.error("APItemplate failed, continuing without PDF:", error);
        storagePath = null;
      }
    } else {
      console.log("APItemplate API key not configured, skipping PDF generation");
    }

    // Create contract record with correct schema
    const contractRow = {
      brand_user_id: userId,
      campaign_id: campaignId,
      influencer_id: influencerId,
      template_id: templateId,
      pdf_url: storagePath,
      status: "DRAFT",
      contract_data: {
        fee: fee || 0,
        deadline: deadline || 'TBD',
        startDate: startDate || new Date().toISOString().split('T')[0],
        endDate: endDate || deadline || 'TBD',
        paymentTerms: paymentTerms || 'Payment due within 30 days',
        specialInstructions: specialInstructions || '',
        contractId,
        apitemplate_transaction_ref: apitemplateResult.transaction_ref,
        generated_at: new Date().toISOString(),
        creation_method: 'apitemplate-function-fixed',
      },
    };

    console.log("Inserting contract:", JSON.stringify(contractRow, null, 2));

    const { data: inserted, error: contractError } = await serviceClient
      .from("contracts")
      .insert(contractRow)
      .select("*")
      .single();

    if (contractError) {
      console.error("Contract insert error:", JSON.stringify(contractError, null, 2));
      throw new Error(`Failed to create contract: ${contractError.message || JSON.stringify(contractError)}`);
    }

    if (!inserted) {
      throw new Error("No contract data returned after insert");
    }

    // Create signed URL for download
    let signedUrl = null;
    if (storagePath) {
      const { data: signedData, error: signedError } = await serviceClient.storage
        .from("contracts")
        .createSignedUrl(storagePath, 3600);

      if (!signedError) {
        signedUrl = signedData?.signedUrl;
      }
    }

    console.log("Contract created successfully:", inserted.id);

    return new Response(
      JSON.stringify({
        success: true,
        contract: inserted,
        downloadUrl: signedUrl,
        apitemplate_transaction_ref: apitemplateResult.transaction_ref,
        message: storagePath ? "Contract PDF generated successfully!" : "Contract created successfully (no PDF generated)",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );

  } catch (error) {
    console.error("Error in APItemplate contract function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        details: error.stack || 'No stack trace available'
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
