import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Simple test contract function called");

    // Service client for database operations
    const serviceClient = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
    );

    // Parse request body
    const {
      campaignId,
      influencerId,
      fee,
      deadline,
      startDate,
      endDate,
      paymentTerms,
      specialInstructions
    } = await req.json();

    console.log("Request data:", {
      campaignId,
      influencerId,
      fee,
      deadline
    });

    // Validate required UUIDs
    if (!campaignId || !influencerId) {
      throw new Error("Campaign ID and Influencer ID are required");
    }

    // Generate a proper UUID for brand_user_id
    const brandUserId = crypto.randomUUID();
    const contractId = crypto.randomUUID();

    console.log("Generated IDs:", {
      brandUserId,
      contractId
    });

    // Create minimal contract record
    const contractRow = {
      brand_user_id: brandUserId,
      campaign_id: campaignId,
      influencer_id: influencerId,
      template_id: 'test-template',
      pdf_url: null,
      status: "DRAFT",
      contract_data: {
        fee: fee || 1000,
        deadline: deadline || new Date().toISOString().split('T')[0],
        startDate: startDate || new Date().toISOString().split('T')[0],
        endDate: endDate || new Date().toISOString().split('T')[0],
        paymentTerms: paymentTerms || 'Test payment terms',
        specialInstructions: specialInstructions || '',
        contractId,
        generated_at: new Date().toISOString(),
        creation_method: 'test-simple-function',
      },
    };

    console.log("Inserting contract:", JSON.stringify(contractRow, null, 2));

    const { data: inserted, error: contractError } = await serviceClient
      .from("contracts")
      .insert(contractRow)
      .select("*")
      .single();

    if (contractError) {
      console.error("Contract insert error:", JSON.stringify(contractError, null, 2));
      throw new Error(`Failed to create contract: ${contractError.message || JSON.stringify(contractError)}`);
    }

    if (!inserted) {
      throw new Error("No contract data returned after insert");
    }

    console.log("Contract created successfully:", inserted.id);

    return new Response(
      JSON.stringify({
        success: true,
        contract: inserted,
        message: "Test contract created successfully",
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );

  } catch (error) {
    console.error("Error in test-contract-simple function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        details: error.stack || 'No stack trace available'
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
