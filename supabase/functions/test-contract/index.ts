import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Test contract function called");

    // Parse request body
    const body = await req.json();
    console.log("Request body:", body);

    // Create service client
    const serviceClient = createClient(
      Deno.env.get("SUPABASE_URL")!,
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!
    );

    // Use real IDs from the request, or default to existing ones
    const campaignId = body.campaignId || '2b765c21-5633-4317-9d22-ced5a4eb66ed'; // CogentX campaign
    const influencerId = body.influencerId || '6abba538-98e3-4c88-88bc-e02adbf5753f'; // Alex Gaming

    // Generate a UUID for brand_user_id (anonymous user)
    const brandUserId = crypto.randomUUID();

    // Generate a simple contract ID
    const contractId = `TEST_${Date.now()}`;

    // Create a basic contract record
    const contractRow = {
      brand_user_id: brandUserId,
      campaign_id: campaignId,
      influencer_id: influencerId,
      template_id: 'test-template',
      pdf_url: null,
      status: "DRAFT",
      contract_data: {
        fee: body.fee || 1000,
        deadline: body.deadline || new Date().toISOString().split('T')[0],
        startDate: body.startDate || new Date().toISOString().split('T')[0],
        endDate: body.endDate || new Date().toISOString().split('T')[0],
        paymentTerms: body.paymentTerms || 'Test payment terms',
        specialInstructions: body.specialInstructions || '',
        contractId,
        generated_at: new Date().toISOString(),
        creation_method: 'test-function',
      },
    };

    console.log("Inserting contract:", contractRow);

    const { data: inserted, error: contractError } = await serviceClient
      .from("contracts")
      .insert(contractRow)
      .select("*")
      .single();

    if (contractError) {
      console.error("Contract insert error:", contractError);
      throw new Error(`Failed to create contract record: ${contractError.message}`);
    }

    console.log("Contract created successfully:", inserted.id);

    return new Response(
      JSON.stringify({
        success: true,
        contract: inserted,
        message: "Test contract created successfully!",
        details: {
          contractId: inserted.id,
          campaignId,
          influencerId,
          fee: body.fee || 1000
        }
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );

  } catch (error) {
    console.error("Error in test-contract function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        details: error.stack || 'No stack trace available'
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
