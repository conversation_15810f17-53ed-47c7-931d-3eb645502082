import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log("Debug test function called");
    console.log("Method:", req.method);
    console.log("Headers:", Object.fromEntries(req.headers.entries()));

    let body = null;
    try {
      body = await req.json();
      console.log("Body:", body);
    } catch (e) {
      console.log("No JSON body or error parsing:", e.message);
    }

    const response = {
      success: true,
      message: "Debug test function working",
      timestamp: new Date().toISOString(),
      method: req.method,
      headers: Object.fromEntries(req.headers.entries()),
      body: body,
      environment: {
        SUPABASE_URL: !!Deno.env.get("SUPABASE_URL"),
        SUPABASE_ANON_KEY: !!Deno.env.get("SUPABASE_ANON_KEY"),
        SUPABASE_SERVICE_ROLE_KEY: !!Deno.env.get("SUPABASE_SERVICE_ROLE_KEY"),
      }
    };

    console.log("Sending response:", response);

    return new Response(
      JSON.stringify(response),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );

  } catch (error) {
    console.error("Error in debug test function:", error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        stack: error.stack
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
