# Edge Function Debug Guide

## Current Status

### ✅ Working via curl
All edge functions work correctly when called directly via curl:
- ✅ `debug-test` - Basic connectivity test
- ✅ `test-contract-simple` - Simple contract creation
- ✅ `create-apitemplate-contract-fixed` - Full APItemplate integration

### ❌ Failing from browser
The same functions return "non-2xx status code" when called from the browser via Supabase client.

## Debugging Steps

### 1. Test Basic Connectivity
Use the new **Simple Edge Function Test** component:
1. Go to Dashboard > Contracts > API Test tab
2. Click "Test Debug Function" to test basic connectivity
3. Check browser console for detailed logs

### 2. Test Contract Functions
Try each function individually:
1. "Test Simple Contract" - Basic contract creation
2. "Test Fixed Contract" - Full APItemplate integration

### 3. Check Browser Console
Look for:
- Network errors
- CORS issues
- Authentication problems
- Detailed error messages

## Possible Issues

### 1. Authentication Mismatch
**Symptom**: Functions work via curl but fail from browser
**Cause**: <PERSON><PERSON><PERSON> might be sending different auth headers
**Solution**: Check if user is authenticated in browser

### 2. CORS Configuration
**Symptom**: Network errors in browser console
**Cause**: CORS headers not properly configured
**Solution**: Verify CORS headers in edge functions

### 3. Environment Variables
**Symptom**: Functions fail with environment errors
**Cause**: Different environment variables between curl and browser
**Solution**: Check environment variable availability

### 4. Request Format Differences
**Symptom**: Functions receive malformed data
**Cause**: Supabase client formats requests differently than curl
**Solution**: Compare request formats

## Testing Functions

### Debug Function
```typescript
// Test basic connectivity
const { data, error } = await supabase.functions.invoke('debug-test', {
  body: { test: 'browser-test' }
});
```

### Simple Contract Function
```typescript
// Test contract creation
const { data, error } = await supabase.functions.invoke('test-contract-simple', {
  body: {
    campaignId: '91636d5d-fd93-4ed5-8bb3-485ffff65152',
    influencerId: 'bf6f9db4-8b5e-472b-ab56-ba22c9673e82',
    fee: 1000,
    deadline: '2024-12-31'
  }
});
```

### Fixed Contract Function
```typescript
// Test full APItemplate integration
const { data, error } = await supabase.functions.invoke('create-apitemplate-contract-fixed', {
  body: {
    campaignId: '91636d5d-fd93-4ed5-8bb3-485ffff65152',
    influencerId: 'bf6f9db4-8b5e-472b-ab56-ba22c9673e82',
    fee: 1000,
    deadline: '2024-12-31',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    paymentTerms: 'Payment due within 30 days',
    specialInstructions: 'Test from browser'
  }
});
```

## Expected Results

### Debug Function
```json
{
  "success": true,
  "message": "Debug test function working",
  "timestamp": "2025-06-09T11:48:07.864Z",
  "method": "POST",
  "body": { "test": "browser-test" },
  "environment": {
    "SUPABASE_URL": true,
    "SUPABASE_ANON_KEY": true,
    "SUPABASE_SERVICE_ROLE_KEY": true
  }
}
```

### Contract Functions
```json
{
  "success": true,
  "contract": {
    "id": "uuid",
    "brand_user_id": "uuid",
    "campaign_id": "uuid",
    "influencer_id": "uuid",
    "status": "DRAFT",
    "contract_data": { ... }
  },
  "message": "Contract created successfully"
}
```

## Troubleshooting Steps

### If Debug Function Fails
1. Check network tab in browser dev tools
2. Verify Supabase URL and anon key in environment
3. Check for CORS errors
4. Verify function is deployed and active

### If Contract Functions Fail
1. Ensure debug function works first
2. Check if campaign and influencer IDs exist
3. Verify database permissions
4. Check function logs (if available)

### If All Functions Fail
1. Check Supabase client configuration
2. Verify environment variables
3. Test with different browser/incognito mode
4. Check network connectivity

## Environment Verification

### Required Environment Variables
```bash
VITE_SUPABASE_URL=https://veeuscozuavvqyjqeljq.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Function URLs
- Debug: `https://veeuscozuavvqyjqeljq.supabase.co/functions/v1/debug-test`
- Simple: `https://veeuscozuavvqyjqeljq.supabase.co/functions/v1/test-contract-simple`
- Fixed: `https://veeuscozuavvqyjqeljq.supabase.co/functions/v1/create-apitemplate-contract-fixed`

## Next Steps

1. **Test in Browser**: Use the Simple Edge Function Test component
2. **Check Console**: Look for detailed error messages
3. **Compare Results**: Note differences between curl and browser results
4. **Report Findings**: Share console logs and error details

## Common Solutions

### Authentication Issues
```typescript
// Check if user is authenticated
const { data: { user } } = await supabase.auth.getUser();
console.log('Current user:', user);
```

### CORS Issues
```typescript
// Verify CORS headers in function response
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};
```

### Environment Issues
```typescript
// Check environment variables
console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('Anon Key present:', !!import.meta.env.VITE_SUPABASE_ANON_KEY);
```

The key is to systematically test each component to isolate where the browser/curl difference occurs.
